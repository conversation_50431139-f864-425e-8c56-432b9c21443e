@echo off
REM Generate WireViz diagram as SVG only
REM Usage: generate_svg_only.bat [yaml_file]

if "%1"=="" (
    echo Usage: generate_svg_only.bat [yaml_file]
    echo Example: generate_svg_only.bat lower_pump_harness_sasquatch_V2.0.yml
    exit /b 1
)

set YAML_FILE=%1
set BASE_NAME=%~n1

echo Generating WireViz diagram for %YAML_FILE%...

REM Generate the DOT file using WireViz
wireviz %YAML_FILE%

REM Check if DOT file was created
if not exist "%BASE_NAME%" (
    echo Error: DOT file %BASE_NAME% was not created
    exit /b 1
)

REM Generate SVG from DOT file
echo Converting DOT to SVG...
dot -Tsvg -o "%BASE_NAME%.svg" "%BASE_NAME%"

if exist "%BASE_NAME%.svg" (
    echo Success: %BASE_NAME%.svg created
    
    REM Optionally remove the PNG file if it was created
    if exist "%BASE_NAME%.png" (
        echo Removing unwanted PNG file...
        del "%BASE_NAME%.png"
    )
) else (
    echo Error: Failed to create SVG file
    exit /b 1
)

echo Done!
